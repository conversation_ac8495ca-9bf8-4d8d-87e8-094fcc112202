<!doctype html>
<!--
 * Cherry Studio Enterprise 发布说明页面
 *
 * 这个 HTML 文件用于显示 Cherry Studio Enterprise 的发布说明和更新日志。
 * 使用 Vue.js 和 Markdown-it 来动态渲染发布内容。
 *
 * 功能：
 * - 显示版本发布信息
 * - 支持 Markdown 格式的发布说明
 * - 响应式设计
 * - 动态内容加载
 *
 * 技术栈：
 * - Vue.js 3 - 前端框架
 * - Tailwind CSS - 样式框架
 * - Markdown-it - Markdown 解析器
 *
 * <AUTHOR> Team
 * @version 1.0.0
 -->
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Cherry Studio Enterprise Release</title>
    <link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet" />
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/typography@0.5.10/dist/typography.min.css"></script>
  </head>

  <body id="app">
    <div :class="isDark ? 'dark-bg' : 'bg'" class="min-h-screen">
      <div class="max-w-4xl mx-auto py-12 px-4">
        <!-- Loading状态 -->
        <div v-if="loading" class="text-center py-8">
          <div
            class="inline-block animate-spin rounded-full h-8 w-8 border-4"
            :class="isDark ? 'border-gray-700 border-t-blue-500' : 'border-gray-300 border-t-blue-500'"></div>
          <p class="mt-4" :class="isDark ? 'text-gray-400' : 'text-gray-600'">正在加载发布信息...</p>
        </div>

        <!-- Error 状态 -->
        <div v-else-if="error" class="text-center py-8">
          <div
            class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded"
            :class="isDark ? 'bg-red-900 border-red-700 text-red-200' : ''">
            <strong class="font-bold">错误：</strong>
            <span class="block sm:inline">{{ error }}</span>
          </div>
        </div>

        <!-- Release 信息 -->
        <div v-else-if="release" class="space-y-8">
          <!-- 头部信息 -->
          <div class="text-center">
            <h1 class="text-4xl font-bold mb-4" :class="isDark ? 'text-white' : 'text-gray-900'">
              Cherry Studio Enterprise
            </h1>
            <div
              class="inline-flex items-center px-4 py-2 rounded-full text-lg font-semibold mb-4"
              :class="isDark ? 'bg-green-900 text-green-200' : 'bg-green-100 text-green-800'">
              {{ release.tag_name }}
            </div>
            <p class="text-lg" :class="isDark ? 'text-gray-400' : 'text-gray-600'">
              发布于 {{ formatDate(release.created_at) }}
            </p>
          </div>

          <!-- 主要内容卡片 -->
          <div
            class="rounded-lg shadow-lg p-8 transition-shadow"
            :class="isDark ? 'bg-black hover:shadow-xl hover:shadow-black' : 'bg-white hover:shadow-xl'">
            <!-- 更新内容 -->
            <div class="mb-8">
              <h2 class="text-2xl font-bold mb-4" :class="isDark ? 'text-white' : 'text-gray-900'">更新内容</h2>
              <div
                class="prose max-w-none"
                :class="isDark ? 'text-gray-300 dark-prose' : 'text-gray-600'"
                v-html="renderMarkdown(release.body)"></div>
            </div>

            <!-- 版本信息 -->
            <div class="pt-6 border-t" :class="isDark ? 'border-gray-700' : 'border-gray-200'">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span class="font-medium" :class="isDark ? 'text-gray-400' : 'text-gray-500'">版本：</span>
                  <span :class="isDark ? 'text-white' : 'text-gray-900'">{{ release.tag_name }}</span>
                </div>
                <div>
                  <span class="font-medium" :class="isDark ? 'text-gray-400' : 'text-gray-500'">提交：</span>
                  <span :class="isDark ? 'text-white' : 'text-gray-900'"
                    >{{ release.target_commitish.substring(0, 8) }}</span
                  >
                </div>
                <div>
                  <span class="font-medium" :class="isDark ? 'text-gray-400' : 'text-gray-500'">预发布：</span>
                  <span :class="isDark ? 'text-white' : 'text-gray-900'">{{ release.prerelease ? '是' : '否' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      const md = window.markdownit({
        breaks: true,
        linkify: true
      })

      const { createApp } = Vue

      createApp({
        data() {
          return {
            release: null,
            loading: true,
            error: null,
            isDark: false
          }
        },
        methods: {
          async fetchRelease() {
            try {
              this.loading = true
              this.error = null
              const response = await fetch('https://releases.enterprise.cherry-ai.com/')
              if (!response.ok) {
                throw new Error('Failed to fetch release')
              }
              this.release = await response.json()
            } catch (err) {
              this.error = 'Error loading release: ' + err.message
            } finally {
              this.loading = false
            }
          },
          formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })
          },
          renderMarkdown(content) {
            if (!content) return ''
            return md.render(content)
          },

          initTheme() {
            // 从 URL 参数获取主题设置
            const url = new URL(window.location.href)
            const theme = url.searchParams.get('theme')
            this.isDark = theme === 'dark'
          }
        },
        mounted() {
          this.initTheme()
          this.fetchRelease()
        }
      }).mount('#app')
    </script>

    <style>
      /* 基础的 Markdown 样式 */
      .prose {
        line-height: 1.6;
      }

      .prose h1 {
        font-size: 1.5em;
        margin: 1em 0;
      }

      .prose h2 {
        font-size: 1.3em;
        margin: 0.8em 0;
      }

      .prose h3 {
        font-size: 1.1em;
        margin: 0.6em 0;
      }

      .prose ul {
        list-style-type: disc;
        margin-left: 1.5em;
        margin-bottom: 1em;
      }

      .prose ol {
        list-style-type: decimal;
        margin-left: 1.5em;
        margin-bottom: 1em;
      }

      .prose code {
        padding: 0.2em 0.4em;
        border-radius: 0.2em;
        font-size: 0.9em;
      }

      .dark .prose code {
        background-color: #1f2937;
      }

      .prose code {
        background-color: #f3f4f6;
      }

      .prose pre code {
        display: block;
        padding: 1em;
        overflow-x: auto;
      }

      .prose a {
        color: #3b82f6;
        text-decoration: underline;
      }

      .dark .prose a {
        color: #60a5fa;
      }

      .prose blockquote {
        border-left: 4px solid #e5e7eb;
        padding-left: 1em;
        margin: 1em 0;
      }

      .dark .prose blockquote {
        border-left-color: #374151;
        color: #9ca3af;
      }

      .dark .prose {
        color: #e5e7eb;
      }

      .dark-bg {
        background-color: #151515;
      }

      .bg {
        background-color: #f2f2f2;
      }
    </style>
  </body>
</html>
