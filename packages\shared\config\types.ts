/**
 * 共享类型定义文件
 *
 * 定义了在 packages/shared 模块中使用的通用类型。
 */

/**
 * 加载器返回结果类型
 *
 * 用于描述文件加载器处理文件后的返回结果
 */
export type LoaderReturn = {
  /** 添加的条目数量 */
  entriesAdded: number
  /** 唯一标识符 */
  uniqueId: string
  /** 唯一标识符数组 */
  uniqueIds: string[]
  /** 加载器类型 */
  loaderType: string
  /** 处理状态（可选） */
  status?: ProcessingStatus
  /** 状态消息（可选） */
  message?: string
  /** 消息来源（可选） */
  messageSource?: 'preprocess' | 'embedding'
}
