/**
 * 主进程与渲染进程通信桥梁
 *
 * 这个文件提供了一个安全的 API 代理，允许渲染进程（webview）调用主进程的方法。
 * 使用 postMessage 和 Promise 机制实现异步通信。
 *
 * 功能：
 * - 创建唯一的消息 ID 用于请求跟踪
 * - 通过 postMessage 发送 API 调用请求
 * - 监听响应消息并解析结果
 * - 提供 Promise 接口简化异步调用
 *
 * 使用方式：
 * window.api.methodName(...args) 返回 Promise
 */

;(() => {
  /** 消息 ID 计数器，确保每个请求都有唯一标识 */
  let messageId = 0

  /**
   * 发送 API 调用请求到主进程
   * @param {string} method - 要调用的方法名
   * @param {...any} args - 方法参数
   * @returns {Promise<any>} 返回 Promise，解析为方法执行结果
   */
  function api(method, ...args) {
    const id = messageId++
    return new Promise((resolve, reject) => {
      pendingCalls.set(id, { resolve, reject })
      window.parent.postMessage({ id, type: 'api-call', method, args }, '*')
    })
  }

  /** 存储待处理的 API 调用 */
  const pendingCalls = new Map()

  /**
   * 监听来自主进程的响应消息
   */
  window.addEventListener('message', (event) => {
    if (event.data.type === 'api-response') {
      const { id, result, error } = event.data
      const pendingCall = pendingCalls.get(id)
      if (pendingCall) {
        if (error) {
          pendingCall.reject(new Error(error))
        } else {
          pendingCall.resolve(result)
        }
        pendingCalls.delete(id)
      }
    }
  })

  /**
   * 创建 API 代理对象
   * 允许通过 window.api.methodName() 的方式调用主进程方法
   */
  window.api = new Proxy(
    {},
    {
      get: (target, prop) => {
        return (...args) => api(prop, ...args)
      }
    }
  )
})()
