/**
 * 工具函数库
 *
 * 提供常用的工具函数，用于处理 URL 参数等操作。
 */

/**
 * 从当前页面 URL 中获取指定的查询参数
 *
 * @param {string} paramName - 要获取的参数名称
 * @returns {string|null} 参数值，如果参数不存在则返回 null
 *
 * @example
 * // 假设当前 URL 为: https://example.com?name=john&age=25
 * const name = getQueryParam('name'); // 返回 'john'
 * const age = getQueryParam('age');   // 返回 '25'
 * const city = getQueryParam('city'); // 返回 null
 */
export function getQueryParam(paramName) {
  const url = new URL(window.location.href)
  const params = new URLSearchParams(url.search)
  return params.get(paramName)
}
